# Page snapshot

```yaml
- link "跳到主内容":
  - /url: "#main-content"
- banner:
  - link "Sanva Studio":
    - /url: /en
  - navigation:
    - link "Home":
      - /url: /en
    - link "About Us":
      - /url: /en/about
    - link "Services":
      - /url: /en/services
    - link "Portfolio":
      - /url: /en/portfolio
    - link "Blog":
      - /url: /en/blog
    - link "Team":
      - /url: /en/team
    - link "Case Studies":
      - /url: /en/case-studies
    - link "Pricing":
      - /url: /en/pricing
    - link "Contact":
      - /url: /en/contact
    - button "🇺🇸English"
- main:
  - heading "Case Studies" [level=2]
  - paragraph: Outcome-driven engineering practices
  - heading "Cross-border E-commerce Growth" [level=3]
  - paragraph: Full-funnel rebuild with A/B experiments improved conversion by 28%.
  - text: Retail & E-commerce +28% CVR +18% Retention +12% Penetration -42% Incidents Next.js Node.js Experiment
- contentinfo:
  - link "Sanva Studio":
    - /url: /en
  - paragraph: Crafting Efficient & Elegant Digital Solutions
  - heading "Navigation" [level=3]
  - list:
    - listitem:
      - link "Home":
        - /url: /en
    - listitem:
      - link "About Us":
        - /url: /en/about
    - listitem:
      - link "Services":
        - /url: /en/services
    - listitem:
      - link "Contact":
        - /url: /en/contact
  - heading "Services" [level=3]
  - list:
    - listitem:
      - link "App Development":
        - /url: /en/services
    - listitem:
      - link "Mini-Program Development":
        - /url: /en/services
    - listitem:
      - link "Backend Development":
        - /url: /en/services
    - listitem:
      - link "Global Platform Development":
        - /url: /en/services
  - heading "Contact" [level=3]
  - list:
    - listitem:
      - link "<EMAIL>":
        - /url: mailto:<EMAIL>
  - paragraph: © 2025 Sanva Software Development Studio. All rights reserved.
- alert
- button "Open Next.js Dev Tools":
  - img
```