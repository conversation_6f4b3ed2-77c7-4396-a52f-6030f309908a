# Page snapshot

```yaml
- link "跳到主内容":
  - /url: "#main-content"
- banner:
  - link "Sanva Studio":
    - /url: /en
  - navigation:
    - link "Home":
      - /url: /en
    - link "About Us":
      - /url: /en/about
    - link "Services":
      - /url: /en/services
    - link "Portfolio":
      - /url: /en/portfolio
    - link "Blog":
      - /url: /en/blog
    - link "Team":
      - /url: /en/team
    - link "Case Studies":
      - /url: /en/case-studies
    - link "Pricing":
      - /url: /en/pricing
    - link "Contact":
      - /url: /en/contact
    - button "🇺🇸English"
- main:
  - heading "Crafting Efficient & Elegant Digital Solutions" [level=1]
  - paragraph: Sanva Software Development Studio specializes in providing professional app development, mini-program development, and backend development services for businesses and individuals.
  - link "Contact Us":
    - /url: /en/contact
    - button "Contact Us"
  - link "Learn More":
    - /url: /en/services
    - button "Learn More"
  - text: 💻 80+ Projects 98% CSAT 12+ Industries 4-8w Avg Cycle
  - heading "Our Services" [level=2]
  - paragraph: Comprehensive software development solutions to meet all your digital needs
  - text: Trusted by clients and partners Acme Globex Umbrella Soylent Initech Stark
  - heading "App Development" [level=3]
  - paragraph: Native iOS/Android or cross-platform app development using React Native, Flutter, and other tech stacks
  - heading "Mini-Program Development" [level=3]
  - paragraph: WeChat Mini-Programs, Alipay Mini-Programs, TikTok Mini-Programs, and more with fast deployment and excellent user experience
  - heading "Backend Development" [level=3]
  - paragraph: RESTful APIs, database design, cloud services (AWS, Alibaba Cloud, etc.) solutions
  - heading "Web Development" [level=3]
  - paragraph: Responsive websites, corporate websites, e-commerce platforms supporting multi-device access
  - heading "Global Platform Development" [level=3]
  - paragraph: Google Play apps, App Store apps, Facebook mini-games, WhatsApp Business, and other global platform development
  - heading "Client Testimonials" [level=2]
  - paragraph: What our clients say about our services
  - paragraph: “Sanva's team is professional and efficient. The mini-program they delivered exceeded our expectations. Customer feedback is very positive!”
  - text: John Smith · Tech Company CEO
  - paragraph: “Working with Sanva has been a pleasure. Their backend solutions are stable and reliable, supporting our rapid business growth.”
  - text: Sarah Johnson · E-commerce Platform Manager
  - heading "Learn More About Us" [level=2]
  - link "About Us Learn about our team and mission":
    - /url: /en/about
    - heading "About Us" [level=3]
    - paragraph: Learn about our team and mission
  - link "Our Services View our detailed service offerings":
    - /url: /en/services
    - heading "Our Services" [level=3]
    - paragraph: View our detailed service offerings
  - link "Contact Us Start your project consultation":
    - /url: /en/contact
    - heading "Contact Us" [level=3]
    - paragraph: Start your project consultation
- contentinfo:
  - link "Sanva Studio":
    - /url: /en
  - paragraph: Crafting Efficient & Elegant Digital Solutions
  - heading "Navigation" [level=3]
  - list:
    - listitem:
      - link "Home":
        - /url: /en
    - listitem:
      - link "About Us":
        - /url: /en/about
    - listitem:
      - link "Services":
        - /url: /en/services
    - listitem:
      - link "Contact":
        - /url: /en/contact
  - heading "Services" [level=3]
  - list:
    - listitem:
      - link "App Development":
        - /url: /en/services
    - listitem:
      - link "Mini-Program Development":
        - /url: /en/services
    - listitem:
      - link "Backend Development":
        - /url: /en/services
    - listitem:
      - link "Global Platform Development":
        - /url: /en/services
  - heading "Contact" [level=3]
  - list:
    - listitem:
      - link "<EMAIL>":
        - /url: mailto:<EMAIL>
  - paragraph: © 2025 Sanva Software Development Studio. All rights reserved.
- alert
- button "Open Next.js Dev Tools":
  - img
```