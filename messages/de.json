{"common": {"contactUs": "Kontakt", "services": "Dienstleistungen", "about": "Über Uns", "home": "Startseite", "learnMore": "<PERSON><PERSON>", "loading": "Lädt...", "submit": "Senden", "submitting": "Wird gesendet..."}, "navigation": {"home": "Startseite", "services": "Dienstleistungen", "about": "Über Uns", "contact": "Kontakt"}, "home": {"hero": {"title": "Effiziente & Elegante Digitale Lösungen Schaffen", "subtitle": "Sanva Software Development Studio spezialisiert sich auf professionelle App-Entwicklung, Mini-Programm-Entwicklung und Backend-Entwicklungsdienstleistungen für Unternehmen und Einzelpersonen.", "cta": "Kontakt"}, "services": {"title": "Unsere Dienstleistungen", "subtitle": "Umfassende Softwareentwicklungslösungen für alle Ihre digitalen Bedürfnisse", "appDev": {"title": "App-Entwicklung", "description": "Native iOS/Android oder plattformübergreifende App-Entwicklung mit React Native, Flutter und anderen Tech-Stacks"}, "miniProgram": {"title": "Mini-Programm-Entwicklung", "description": "WeChat Mini-Programme, Alipay Mini-Programme, TikTok Mini-Programme und mehr mit schneller Bereitstellung und ausgezeichneter Benutzererfahrung"}, "backend": {"title": "Backend-Entwicklung", "description": "RESTful APIs, Datenbankdesign, Cloud-Services (AWS, Alibaba Cloud, etc.) Lösungen"}, "webDev": {"title": "Web-Entwicklung", "description": "Responsive Websites, Unternehmenswebsites, E-Commerce-Plattformen mit Multi-Device-Zugang"}, "globalPlatforms": {"title": "Globale Plattform-Entwicklung", "description": "Google Play Apps, App Store Apps, Facebook Mini-Spiele, WhatsApp Business und andere globale Plattformentwicklung"}}, "testimonials": {"title": "Kundenstimmen", "subtitle": "Was unsere Kunden über unsere Dienstleistungen sagen"}, "quickNav": {"title": "Mehr Über Uns Erfahren"}}, "services": {"title": "Unsere Dienstleistungen", "subtitle": "Wir bieten professionelle Softwareentwicklungsdienstleistungen einschließlich App-Entwicklung, Mini-Programm-Entwicklung und Backend-Entwicklung", "sectionTitle": "Umfassende Softwareentwicklungslösungen", "sectionSubtitle": "Egal ob Sie mobile Apps, Mini-Programme oder Backend-Systeme benötigen, wir bieten qualitativ hochwertige Entwicklungsdienstleistungen, um Ihrem Unternehmen bei der digitalen Transformation zu helfen.", "appDevelopment": {"title": "App-Entwicklung", "description": "Wir bieten native iOS- und Android-App-Entwicklungsdienstleistungen sowie Cross-Platform-App-Entwicklung mit modernen Technologien.", "features": {"native": "Native App-Entwicklung (iOS/Android)", "crossPlatform": "Cross-Platform App-Entwicklung", "uiUx": "UI/UX Design", "maintenance": "App-Wartung & Updates", "storeSupport": "App Store Einreichungsunterstützung"}}, "miniProgram": {"title": "Mini-Programm-Entwicklung", "description": "Wir konzentrieren uns auf die Entwicklung verschiedener Mini-Programme einschließlich WeChat, Alipay und TikTok Mini-Programme, um Unternehmen zu helfen, <PERSON><PERSON><PERSON> schnell zu erreichen.", "features": {"wechat": "WeChat Mini-Programm-Entwicklung", "alipay": "Alipay Mini-Programm-Entwicklung", "douyin": "TikTok Mini-Programm-Entwicklung", "multiPlatform": "Multi-Platform Mini-Programm-Lösungen", "ecommerce": "Mini-Programm E-Commerce & Zahlungsintegration"}}, "backend": {"title": "Backend-Entwicklung", "description": "Wir bieten zuverlässige, sichere und leistungsstarke Backend-Entwicklungsdienstleistungen, um Ihre Anwendungen mit starker Datenunterstützung zu versorgen.", "features": {"api": "RESTful API-Entwicklung", "database": "Datenbankdesign & Optimierung", "auth": "Benutzerauthentifizierung & Autorisierung", "cloud": "Cloud-Service-Integration (AWS, Alibaba Cloud, etc.)", "server": "Serverkonfiguration & Wartung"}}, "globalPlatforms": {"title": "Globale Plattform-Entwicklung", "description": "Wir bieten Entwicklungsdienstleistungen für globale Plattformen, um Ihren Produkten zu helfen, <PERSON><PERSON><PERSON> weltweit zu erreichen.", "features": {"googlePlay": "Google Play App-Entwicklung", "appStore": "App Store App-Entwicklung", "facebook": "Facebook Mini-Spiel-Entwicklung", "whatsapp": "WhatsApp Business Integration", "telegram": "Telegram Bot-Entwicklung", "instagram": "Instagram API Integration", "twitter": "Twitter/X API Integration", "linkedin": "LinkedIn App-Entwicklung"}}, "process": {"title": "Unser Entwicklungsprozess", "subtitle": "Transparenter und effizienter Entwicklungsprozess, der reibungslosen Projektfortschritt und rechtzeitige Lieferung gewährleistet", "steps": {"analysis": {"title": "Anforderungsanalyse", "description": "Tiefes Verständnis Ihrer Geschäftsanforderungen, Definition von Projektzielen und Funktionsumfang"}, "design": {"title": "Design & Planung", "description": "Erstellung technischer Lösungen und Design-Prototypen zur Gewährleistung optimaler Benutzererfahrung"}, "development": {"title": "Entwicklungsimplementierung", "description": "Entwicklung gemäß Designplänen mit regelmäßigen Fortschrittsberichten und Ergebnissen"}, "delivery": {"title": "Test & Lieferung", "description": "Umfassende Tests der Anwendungsfunktionen, Qualitätssicherung vor Deployment und Lieferung"}}}}, "contact": {"title": "Kontakt", "subtitle": "Wir freuen uns darauf, <PERSON><PERSON><PERSON> Bedürfnis<PERSON> zu hören und jederzeit professionelle Unterstützung zu bieten", "methods": {"title": "Kontaktinformationen", "email": "E-Mail", "workTime": "Geschäftszeiten", "workHours": "Montag bis Freitag 9:00 - 18:00"}, "followUs": "Folgen Sie Uns", "form": {"title": "Nachricht Senden", "subtitle": "Wir freuen uns auf Ihre Nachricht. Unser professionelles Team wird Ihnen maßgeschneiderte Lösungen anbieten", "name": "Name", "email": "E-Mail", "phone": "Telefon (Optional)", "message": "Anforderungsbeschreibung", "required": "*", "send": "💌 Jetzt Senden"}}, "about": {"title": "Über Uns", "subtitle": "Erfahren Sie mehr über den Hintergrund und die professionellen Fähigkeiten von Sanva Software Development Studio", "introduction": {"title": "Studio-Vorstellung", "paragraphs": {"first": "Sanva Software Development Studio wurde 2023 gegründet und spezialisiert sich darauf, hochwertige Software-Lösungen für Unternehmen und Privatpersonen anzubieten. Unsere Dienstleistungen umfassen mobile App-Entwicklung, Mini-Programm-Entwicklung und Backend-System-Entwicklung.", "second": "Unser Team besteht aus erfahrenen Entwicklungsingenieuren und Designern, jeder mit soliden technischen Fähigkeiten und innovativem Denken. Wir konzentrieren uns auf modernste Technologieentwicklung, lernen und wenden kontinuierlich neue Technologien an, um sicherzustellen, dass wir unseren Kunden die höchste Servicequalität bieten.", "third": "Bei Sanva Software glauben wir, dass Technologie den Menschen dienen und Wert für Unternehmen schaffen sollte. Wir sind verpflichtet, echte Probleme mit Technologie zu lösen, Kunden bei der digitalen Transformation zu helfen, die operative Effizienz zu verbessern und die Marktkonkurrenzfähigkeit zu stärken."}}, "values": {"title": "Unsere Grundwerte", "subtitle": "Diese Werte leiten unsere tägliche Arbeit und helfen uns, unseren Kunden die höchste Servicequalität zu bieten", "items": {"professional": {"title": "Professionell", "description": "Wir haben umfangreiche technische Erfahrung und Branchenwissen, um unseren Kunden professionelle Software-Lösungen anzubieten."}, "efficient": {"title": "Effizient", "description": "Wir konzentrieren uns auf Entwicklungseffizienz und Projektfortschritt-Management, um eine pünktliche Lieferung hochwertiger Produkte zu gewährleisten."}, "customerFirst": {"title": "<PERSON><PERSON>", "description": "Wir konzentrieren uns auf Kundenbedürfnisse und bieten personalisierte Lösungen und aufmerksamen Kundendienst."}}}, "team": {"title": "Unser Team", "subtitle": "Zusammengesetzt aus erfahrenen Fachleuten, die sich der Bereitstellung der höchsten Servicequalität für Sie widmen", "members": {"founder": {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON><PERSON> / <PERSON>", "description": "Mit 10 Jahren Software-Entwicklungserfahrung, spezialisiert auf mobile App- und Mini-Programm-Entwicklung."}, "designer": {"name": "<PERSON>", "role": "UI/UX Designer", "description": "Geschickt in der Erstellung schöner und benutzerfreundlicher Schnittstellen, fokussiert auf jedes Detail der Benutzererfahrung."}, "backend": {"name": "<PERSON>", "role": "Backend-Entwickler", "description": "Experte für Cloud-Services und Datenbankdesign, erstellt stabile und effiziente Backend-Systeme."}}}}}