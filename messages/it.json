{"common": {"contactUs": "Con<PERSON><PERSON><PERSON>", "services": "<PERSON><PERSON><PERSON>", "about": "Chi siamo", "home": "Home", "learnMore": "Scopri di più", "loading": "Caricamento...", "submit": "Invia", "submitting": "Invio in corso..."}, "navigation": {"home": "Home", "services": "<PERSON><PERSON><PERSON>", "about": "Chi siamo", "contact": "<PERSON><PERSON><PERSON>"}, "home": {"hero": {"title": "Creazione di soluzioni digitali efficienti ed eleganti", "subtitle": "Sanva Software Development Studio è specializzato nel fornire servizi professionali di sviluppo app, mini-programmi e sviluppo backend per aziende e privati.", "cta": "Con<PERSON><PERSON><PERSON>"}, "services": {"title": "I nostri servizi", "subtitle": "Soluzioni complete di sviluppo software per soddisfare tutte le tue esigenze digitali", "appDev": {"title": "Sviluppo App", "description": "Sviluppo di app native iOS/Android o multipiattaforma utilizzando React Native, Flutter e altri stack tecnologici"}, "miniProgram": {"title": "Sviluppo Mini-Programmi", "description": "Mini-programmi WeChat, <PERSON><PERSON><PERSON>, TikTok e altri con distribuzione rapida ed eccellente esperienza utente"}, "backend": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "API RESTful, progettazione database, servizi cloud (AWS, Alibaba Cloud, ecc.)"}, "webDev": {"title": "Sviluppo Web", "description": "Siti web responsive, siti aziendali, piattaforme e-commerce con accesso multi-dispositivo"}, "globalPlatforms": {"title": "<PERSON><PERSON><PERSON><PERSON> Piattaforme Globali", "description": "App Google Play, App Store, mini-giochi Facebook, WhatsApp Business e altre piattaforme globali"}}, "testimonials": {"title": "Testimonianze clienti", "subtitle": "Cosa dicono i nostri clienti dei nostri servizi"}, "quickNav": {"title": "Scopri di più su di noi"}}, "services": {"title": "I nostri servizi", "subtitle": "Forniamo servizi professionali di sviluppo software incluso sviluppo app, mini-programmi e backend", "sectionTitle": "Soluzioni complete di sviluppo software", "sectionSubtitle": "Che tu abbia bisogno di app mobili, mini-programmi o sistemi backend, forniamo servizi di sviluppo di qualità per aiutare la tua azienda a raggiungere la trasformazione digitale.", "appDevelopment": {"title": "Sviluppo App", "description": "Forniamo servizi di sviluppo di app native per iOS e Android, oltre allo sviluppo di app multipiattaforma utilizzando tecnologie moderne.", "features": {"native": "Sviluppo App Native (iOS/Android)", "crossPlatform": "Sviluppo App Multipiattaforma", "uiUx": "Design UI/UX", "maintenance": "Manutenzione e Aggiornamenti App", "storeSupport": "Supporto Pubblicazione App Store"}}, "miniProgram": {"title": "Sviluppo Mini-Programmi", "description": "Ci concentriamo sullo sviluppo di vari mini-programmi inclusi WeChat, Alipay e TikTok per aiutare le aziende a raggiungere rapidamente gli utenti.", "features": {"wechat": "Sviluppo Mini-Programmi WeChat", "alipay": "Sviluppo Mini-<PERSON><PERSON>", "douyin": "Sviluppo Mini-Programmi <PERSON>", "multiPlatform": "Soluzioni Mini-Programmi Multipiattaforma", "ecommerce": "Integrazione E-commerce e Pagamenti Mini-Programmi"}}, "backend": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Forniamo servizi di sviluppo backend affidabili, sicuri e ad alte prestazioni per potenziare le tue applicazioni.", "features": {"api": "Sviluppo API RESTful", "database": "Progettazione e Ottimizzazione Database", "auth": "Autenticazione e Autorizzazione Utenti", "cloud": "Integrazione Servizi Cloud (AWS, Alibaba Cloud, ecc.)", "server": "Configurazione e Manutenzione Server"}}, "globalPlatforms": {"title": "<PERSON><PERSON><PERSON><PERSON> Piattaforme Globali", "description": "Forniamo servizi di sviluppo per piattaforme globali per aiutare i tuoi prodotti a raggiungere utenti in tutto il mondo.", "features": {"googlePlay": "Sviluppo App Google Play", "appStore": "Sviluppo App App Store", "facebook": "Sviluppo Mini-Giochi Facebook", "whatsapp": "Integrazione WhatsApp Business", "telegram": "Sviluppo Bot Telegram", "instagram": "Integrazione API Instagram", "twitter": "Integrazione API Twitter/X", "linkedin": "Sviluppo App LinkedIn"}}, "process": {"title": "Il nostro processo di sviluppo", "subtitle": "Processo di sviluppo trasparente ed efficiente che garantisce progresso fluido del progetto e consegna puntuale", "steps": {"analysis": {"title": "<PERSON><PERSON><PERSON>", "description": "Comprensione approfondita delle esigenze della tua azienda, definizione degli obiettivi del progetto e ambito delle funzionalità"}, "design": {"title": "Design e Pianificazione", "description": "Creazione di soluzioni tecniche e prototipi di design per garantire esperienza utente ottimale"}, "development": {"title": "Implementazione Sviluppo", "description": "Sviluppo secondo i piani di design con rapporti regolari sui progressi e deliverable"}, "delivery": {"title": "Test e Consegna", "description": "Test completi delle funzionalità dell'applicazione, garanzia di qualità prima del deployment e consegna"}}}}, "contact": {"title": "Con<PERSON><PERSON><PERSON>", "subtitle": "Non vediamo l'ora di sentire le tue esigenze e fornire supporto professionale in qualsiasi momento", "methods": {"title": "Informazioni di contatto", "email": "Email", "workTime": "Orari di lavoro", "workHours": "Lunedì - Venerdì 9:00 - 18:00"}, "followUs": "<PERSON><PERSON><PERSON>", "form": {"title": "Invia messaggio", "subtitle": "Non vediamo l'ora del tuo messaggio. Il nostro team professionale ti fornirà soluzioni personalizzate", "name": "Nome", "email": "Email", "phone": "Telefono (Opzionale)", "message": "Descrizione requisiti", "required": "*", "send": "💌 Invia ora", "success": {"title": "🎉 Grazie per il tuo messaggio!", "message": "Abbiamo ricevuto il tuo messaggio. Il nostro team professionale ti contatterà entro 24 ore.", "urgent": "Per esigenze urgenti, invia un'email <NAME_EMAIL>"}, "errors": {"nameRequired": "Per favore inserisci il tuo nome", "emailRequired": "Per favore inserisci la tua email", "emailInvalid": "Per favore inserisci un indirizzo email valido", "messageRequired": "Per favore inserisci la descrizione dei tuoi requisiti"}}, "faq": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Ecco alcune domande frequenti dei nostri clienti. Se hai altre domande, non esitare a contattarci", "questions": {"timeline": {"q": "Quanto tempo richiede tipicamente lo sviluppo di un progetto?", "a": "I tempi di sviluppo del progetto dipendono dalla scala e complessità del progetto. I mini-programmi semplici possono richiedere 2-4 set<PERSON><PERSON><PERSON>, mentre le applicazioni complesse possono richiedere 2-3 mesi. Forniamo stime temporali dettagliate prima dell'inizio del progetto."}, "pricing": {"q": "Quali sono i vostri standard di prezzo?", "a": "Determiniamo i prezzi basandoci sulla complessità del progetto, numero di funzionalità e tempo di sviluppo. Forniamo servizi gratuiti di valutazione e preventivo del progetto. Per favore contattaci per informazioni dettagliate."}, "maintenance": {"q": "Fornite servizi di manutenzione dopo lo sviluppo?", "a": "<PERSON><PERSON>, forniamo supporto tecnico e servizi di manutenzione dopo la consegna del progetto. Di solito forniamo 1-3 mesi di periodo di manutenzione gratuito, seguito da contratti di manutenzione a lungo termine."}, "modification": {"q": "Potete modificare app o mini-programmi esistenti?", "a": "Sì. Possiamo prendere in carico e modificare progetti esistenti, ottimizzare funzionalità o risolvere problemi. Prima valutiamo il codice, poi forniamo piani di modifica e preventivi."}}}}, "about": {"title": "Chi siamo", "subtitle": "Scopri la storia e le capacità professionali di Sanva Software Development Studio", "introduction": {"title": "Presentazione dello Studio", "paragraphs": {"first": "Sanva Software Development Studio è stato fondato nel 2023, specializzandosi nel fornire soluzioni software di alta qualità per aziende e individui. I nostri servizi coprono lo sviluppo di app mobili, lo sviluppo di mini-programmi e lo sviluppo di sistemi backend.", "second": "Il nostro team è composto da ingegneri di sviluppo e designer esperti, ognuno con solide competenze tecniche e pensiero innovativo. Ci concentriamo sullo sviluppo di tecnologie all'avanguardia, imparando e applicando continuamente nuove tecnologie per garantire che forniamo servizi della più alta qualità ai nostri clienti.", "third": "In Sanva Software, crediamo che la tecnologia debba servire le persone e creare valore per le aziende. Siamo impegnati a risolvere problemi reali con la tecnologia, aiutando i clienti a raggiungere la trasformazione digitale, migliorare l'efficienza operativa e rafforzare la competitività del mercato."}}, "values": {"title": "I Nostri Valori Fondamentali", "subtitle": "Questi valori guidano il nostro lavoro quotidiano e ci aiutano a fornire servizi della più alta qualità ai nostri clienti", "items": {"professional": {"title": "Professionale", "description": "Abbiamo vasta esperienza tecnica e conoscenza del settore per fornire soluzioni software professionali ai nostri clienti."}, "efficient": {"title": "Efficiente", "description": "Ci concentriamo sull'efficienza dello sviluppo e sulla gestione del progresso del progetto per garantire la consegna tempestiva di prodotti di alta qualità."}, "customerFirst": {"title": "Cliente Prima di Tutto", "description": "Ci concentriamo sulle esigenze del cliente, fornendo soluzioni personalizzate e servizio post-vendita attento."}}}, "team": {"title": "Il Nostro Team", "subtitle": "Composto da professionisti esperti dedicati a fornirti servizi della più alta qualità", "members": {"founder": {"name": "<PERSON>", "role": "Fondatore / Sviluppatore Principale", "description": "Con 10 anni di esperienza nello sviluppo software, specializzato nello sviluppo di app mobili e mini-programmi."}, "designer": {"name": "<PERSON>", "role": "Designer UI/UX", "description": "Abile nel creare interfacce belle e user-friendly, concentrandosi su ogni dettaglio dell'esperienza utente."}, "backend": {"name": "<PERSON>", "role": "Sviluppatore Backend", "description": "Esperto in servizi cloud e progettazione di database, costruendo sistemi backend stabili ed efficienti."}}}}}