@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #0f172a;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  /* 统一为苹果系统字体栈，贴合 Apple HIG */
  --font-sans: -apple-system, BlinkMacSystemFont, "SF Pro Text", "SF Pro Display", system-ui, "Segoe UI", Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif;
  --font-display: -apple-system, BlinkMacSystemFont, "SF Pro Display", system-ui, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  --font-mono: JetBrains Mono, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #020617;
    --foreground: #f8fafc;
  }
}

* {
  border-color: #e2e8f0;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  /* 苹果系统字体栈，优先使用 SF Pro */
  font-family: -apple-system, BlinkMacSystemFont, "SF Pro Text", "SF Pro Display", Segoe UI, Roboto, Helvetica, Arial, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", sans-serif;


  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 渐变文本工具类（苹果风不鼓励在正文/品牌标题使用，保留但减少引用） */
.gradient-text {
  background: linear-gradient(135deg, #3b82f6, #d946ef);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 新增：苹果风格发丝分隔线与毛玻璃容器 */
.hairline { border-color: rgba(60,60,67,0.29); }
.surface-translucent { background-color: rgba(255,255,255,0.7); backdrop-filter: saturate(180%) blur(20px); }
.surface-translucent-dark { background-color: rgba(28,28,30,0.5); backdrop-filter: saturate(180%) blur(20px); }

/* 玻璃态效果 */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.15);
}

/* 按钮动画 */
.btn-animate {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.btn-animate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-animate:hover::before {
  left: 100%;
}

/* 响应式字体大小 */
.text-responsive {
  font-size: clamp(1rem, 2.5vw, 1.5rem);
}

.text-responsive-lg {
  font-size: clamp(1.5rem, 4vw, 3rem);
}

.text-responsive-xl {
  font-size: clamp(2rem, 5vw, 4rem);
}

/* 细腻噪点与网格叠层，营造层次与空间感 */
.bg-noise {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='120' height='120' viewBox='0 0 120 120'%3E%3Cfilter id='n'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.8' numOctaves='4' stitchTiles='stitch'/%3E%3CfeColorMatrix type='saturate' values='0'/%3E%3CfeComponentTransfer%3E%3CfeFuncA type='linear' slope='0.04'/%3E%3C/feComponentTransfer%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23n)'/%3E%3C/svg%3E");
  opacity: 0.35;
}

.bg-grid {
  background-image: linear-gradient(to right, rgba(100,116,139,0.08) 1px, transparent 1px),
                    linear-gradient(to bottom, rgba(100,116,139,0.08) 1px, transparent 1px);
  background-size: 32px 32px;
}

/* 渐变遮罩，配合图片/视频上叠加 */
.gradient-overlay {
  position: relative;
}
.gradient-overlay::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, rgba(2,6,23,0) 0%, rgba(2,6,23,0.6) 100%);
  pointer-events: none;
}

/* 输入焦点可视态与轻微浮起动画 */
.input-elevate {
  transition: box-shadow 0.25s ease, transform 0.2s ease;
}
.input-elevate:focus {
  box-shadow: 0 10px 30px -12px rgba(59,130,246,0.25);
  transform: translateY(-1px);
}

/* 更柔和的阴影层级补充 */
.shadow-soft-2 { box-shadow: 0 8px 24px -12px rgba(0,0,0,0.12); }
.shadow-floating { box-shadow: 0 20px 50px -24px rgba(0,0,0,0.35); }

/* 图像悬停轻缩放 */
.hover-zoom { transition: transform .4s cubic-bezier(0.4, 0, 0.2, 1); }
.hover-zoom:hover { transform: scale(1.03); }


/* Skip link 样式：仅在聚焦时显示，提升键盘可访问性 */
/* Confirmed via mcp-feedback-enhanced */
.skip-link {
  position: absolute;
  left: -999px;
  top: 0;
  background: #111827;
  color: white;
  padding: 12px 16px;
  border-radius: 0 0 8px 8px;
  z-index: 10000;
}
.skip-link:focus {
  left: 0;
}

/* 优先尊重用户减少动态偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.001ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.001ms !important;
    scroll-behavior: auto !important;
  }
}
