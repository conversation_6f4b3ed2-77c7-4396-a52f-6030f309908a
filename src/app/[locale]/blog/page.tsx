'use client';
import React from 'react';
import Container from '@/components/ui/Container';
import SectionHeader from '@/components/ui/SectionHeader';
import BlogCard from '@/components/ui/BlogCard';
import { useParams } from 'next/navigation';
import type { Locale } from '@/lib/i18n';

export default function Blog() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  const isZh = localeTyped === 'zh';

  const posts = [
    {
      title: isZh ? '用 Next.js 15 构建多语言网站' : 'Building a Multilingual Site with Next.js 15',
      excerpt: isZh ? '实战演示如何基于 App Router 快速搭建多语言路由与 SEO。' : 'A practical guide to set up i18n routes with App Router and improve SEO.',
      date: '2024-12-01',
      tags: ['Next.js', 'i18n', 'SEO']
    },
    {
      title: isZh ? 'Tailwind CSS 4 渐变与阴影最佳实践' : 'Tailwind CSS 4 Gradients and Shadows Best Practices',
      excerpt: isZh ? '通过语义化颜色与动画令界面更灵动。' : 'Make interfaces lively with semantic colors and animations.',
      date: '2024-11-20',
      tags: ['Tailwind', 'Design']
    },
  ];

  return (
    <Container>
      <div className="py-16">
        <SectionHeader
          title={isZh ? '博客 / 资讯' : 'Blog / News'}
          subtitle={isZh ? '分享我们的技术实践与设计思考' : 'Sharing our engineering practices and design thoughts'}
        />
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {posts.map((p) => (
            <BlogCard key={p.title} {...p} />
          ))}
        </div>
      </div>
    </Container>
  );
}

