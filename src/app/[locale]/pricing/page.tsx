'use client';
import React from 'react';
import Container from '@/components/ui/Container';
import SectionHeader from '@/components/ui/SectionHeader';
import PricingCard from '@/components/ui/PricingCard';
import Testimonial from '@/components/ui/Testimonial';
import { useParams } from 'next/navigation';
import type { Locale } from '@/lib/i18n';

export default function Pricing() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  const plans = [
    { name: isZh ? '基础版' : 'Starter', price: isZh ? '面议' : 'Contact Us', features: [isZh ? '企业官网/展示页' : 'Company site/Landing', 'SEO', isZh ? '基础动效' : 'Micro-interactions', isZh ? '7 天上线' : '7d go-live'] },
    { name: isZh ? '专业版' : 'Pro', price: isZh ? '面议' : 'Contact Us', features: [isZh ? '多语言/多地区' : 'i18n', isZh ? 'SSR/边缘渲染' : 'SSR/Edge', isZh ? '接口/后台' : 'APIs/Admin', isZh ? '性能优化' : 'Perf'], highlight: true },
    { name: isZh ? '定制版' : 'Custom', price: isZh ? '面议' : 'Contact Us', features: [isZh ? '从 0-1 产品定制' : '0-1 product', isZh ? '端到端交付' : 'E2E delivery', isZh ? '数据分析' : 'Analytics', isZh ? 'A/B 实验' : 'Experiments'] },
  ];

  const quotes = [
    { quote: isZh ? '他们的交付速度和质量都超出了预期。' : 'Speed and quality exceeded our expectations.', author: 'Founder A', role: isZh ? '创始人' : 'Founder' },
    { quote: isZh ? '在移动端体验和性能优化上非常专业。' : 'Very professional on mobile UX and performance.', author: 'PM B', role: 'PM' },
  ];

  return (
    <Container>
      <div className="py-16">
        <SectionHeader title={isZh ? '价格方案' : 'Pricing'} subtitle={isZh ? '灵活透明的服务定价' : 'Flexible and transparent pricing'} />
        <div className="grid md:grid-cols-3 gap-6">
          {plans.map((p) => (
            <PricingCard key={p.name} {...p} onSelect={() => { window.location.href = `/${locale}/contact`; }} />
          ))}
        </div>
        <div className="mt-16 grid md:grid-cols-2 gap-6">
          {quotes.map((q) => (
            <Testimonial key={q.quote} {...q} />
          ))}
        </div>
      </div>
    </Container>
  );
}

