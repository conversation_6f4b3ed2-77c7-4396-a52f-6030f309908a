'use client';

import Link from "next/link";
import { motion } from "framer-motion";
import { useParams } from "next/navigation";
import { t, type Locale } from '@/lib/i18n';
import {
  AppDevIcon,
  MiniProgramIcon,
  BackendIcon,
  WebDevIcon,
  GlobalPlatformsIcon,
  StarIcon,
  QuoteIcon,
  NavigationIcons,
  ContactIcons
} from '@/components/ui/Icons';
import Card, { CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Container from '@/components/ui/Container';
import Stats from '@/components/ui/Stats';
import Testimonial from '@/components/ui/Testimonial';

// 动画配置
const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
};

// 服务卡片组件
const ServiceCard = ({
  title,
  description,
  IconComponent
}: {
  title: string;
  description: string;
  IconComponent: React.ComponentType<{ className?: string }>;
}) => (
  <Card hover className="group">
    <CardContent className="text-center">
      <div className="mb-6 flex justify-center">
        <div className="p-4 bg-gradient-to-br from-primary-50 to-primary-100 rounded-2xl group-hover:from-primary-100 group-hover:to-primary-200 transition-all duration-300">
          <IconComponent className="w-8 h-8 text-primary-600" />
        </div>
      </div>
      <CardTitle size="md" className="mb-3 group-hover:text-primary-600 transition-colors">
        {title}
      </CardTitle>
      <CardDescription className="text-sm leading-relaxed">
        {description}
      </CardDescription>
    </CardContent>
  </Card>
);

// 评价卡片组件替换为统一 Testimonial 组件，保持视觉一致性
const TestimonialCard = ({ name, role, content }: { name: string; role: string; content: string }) => (
  <Testimonial quote={content} author={name} role={role} />
);

export default function Home() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;

  // 服务数据
  const services = [
    {
      title: t('home.services.appDev.title', localeTyped),
      description: t('home.services.appDev.description', localeTyped),
      IconComponent: AppDevIcon
    },
    {
      title: t('home.services.miniProgram.title', localeTyped),
      description: t('home.services.miniProgram.description', localeTyped),
      IconComponent: MiniProgramIcon
    },
    {
      title: t('home.services.backend.title', localeTyped),
      description: t('home.services.backend.description', localeTyped),
      IconComponent: BackendIcon
    },
    {
      title: t('home.services.webDev.title', localeTyped),
      description: t('home.services.webDev.description', localeTyped),
      IconComponent: WebDevIcon
    },
    {
      title: t('home.services.globalPlatforms.title', localeTyped),
      description: t('home.services.globalPlatforms.description', localeTyped),
      IconComponent: GlobalPlatformsIcon
    }
  ];

  // 客户评价
  const testimonials = localeTyped === 'zh' ? [
    {
      name: "王先生",
      role: "科技公司 CEO",
      content: "三娃软件团队专业高效，交付的小程序超出了我们的预期。客户反馈非常积极！"
    },
    {
      name: "李女士",
      role: "电商平台负责人",
      content: "与三娃软件的合作非常愉快，他们提供的后端解决方案稳定可靠，支持了我们业务的快速发展。"
    }
  ] : [
    {
      name: "John Smith",
      role: "Tech Company CEO",
      content: "Sanva's team is professional and efficient. The mini-program they delivered exceeded our expectations. Customer feedback is very positive!"
    },
    {
      name: "Sarah Johnson",
      role: "E-commerce Platform Manager",
      content: "Working with Sanva has been a pleasure. Their backend solutions are stable and reliable, supporting our rapid business growth."
    }
  ];

  return (
    <>
      {/* Hero 区域 - 增强空间层次与质感 */}
      <section className="relative py-16 md:py-24 bg-white text-neutral-900">
        {/* 苹果风格：去除装饰纹理，保持极简与留白 */}

        <Container>
          <div className="md:flex md:items-center md:justify-between">
            <div className="md:w-1/2">
              <motion.h1
                className="text-4xl md:text-6xl font-bold mb-6 leading-tight"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8 }}
              >
                <span className="block text-responsive-xl font-display">
                  {t('home.hero.title', localeTyped)}
                </span>
              </motion.h1>
              <motion.p
                className="text-xl md:text-2xl mb-8 text-primary-100 leading-relaxed"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                {t('home.hero.subtitle', localeTyped)}
              </motion.p>
              <motion.div
                className="flex flex-col sm:flex-row gap-4"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.4 }}
              >
                <Link href={`/${locale}/contact`}>
                  <Button
                    size="lg"
                    variant="primary"
                    gradient
                    icon={NavigationIcons.Arrow}
                    iconPosition="right"
                    className="shadow-soft"
                  >
                    {t('home.hero.cta', localeTyped)}
                  </Button>
                </Link>
                <Link href={`/${locale}/services`}>
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-neutral-300 text-neutral-800 hover:bg-neutral-100 hover:text-neutral-900"
                  >
                    {localeTyped === 'zh' ? '了解服务' : 'Learn More'}
                  </Button>
                </Link>
              </motion.div>
            </div>
            <div className="hidden md:block md:w-1/2 mt-12 md:mt-0">
              <motion.div
                className="relative h-80 flex items-center justify-center"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 1, delay: 0.6 }}
              >
                <div className="relative">
                  <div className="w-64 h-64 bg-gradient-to-br from-secondary-400 to-secondary-600 rounded-3xl shadow-2xl rotate-6 animate-bounce-gentle"></div>
                  <div className="absolute inset-0 w-64 h-64 bg-gradient-to-br from-primary-400 to-primary-600 rounded-3xl shadow-2xl -rotate-6 flex items-center justify-center">
                    <div className="text-6xl">💻</div>
                  </div>
                </div>
              </motion.div>
            </div>
      {/* 关键指标区域 */}
      <section className="py-12 bg-white">
        <Container>
          <Stats
            items={[
              { label: localeTyped === 'zh' ? '交付项目' : 'Projects', value: '80+' },
              { label: localeTyped === 'zh' ? '客户满意度' : 'CSAT', value: '98%' },
              { label: localeTyped === 'zh' ? '覆盖行业' : 'Industries', value: '12+' },
              { label: localeTyped === 'zh' ? '平均周期' : 'Avg Cycle', value: '4-8w' },
            ]}
            className="max-w-5xl mx-auto"
          />
        </Container>
      </section>

          </div>
        </Container>
      </section>

      {/* 服务介绍区域 */}
      <section className="py-20 bg-gradient-to-br from-neutral-50 to-neutral-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl md:text-5xl font-bold font-display gradient-text mb-6">
              {t('home.services.title', localeTyped)}
            </h2>
            <p className="text-xl text-neutral-600 max-w-4xl mx-auto leading-relaxed">
              {t('home.services.subtitle', localeTyped)}
            </p>
          </motion.div>
      {/* 品牌墙（可选） */}
      <section className="py-12 bg-neutral-50">
        <Container>
          <div className="text-center mb-8 text-neutral-500 text-sm">{localeTyped === 'zh' ? '被以下客户与伙伴所信任' : 'Trusted by clients and partners'}</div>
          {/* 使用简单静态徽标占位，后续可替换为真实品牌 */}
          <div className="overflow-hidden">
            <div className="flex animate-marquee gap-12">
              {['Acme', 'Globex', 'Umbrella', 'Soylent', 'Initech', 'Stark'].map((brand) => (
                <div key={brand} className="text-neutral-400 hover:text-neutral-600 transition-colors text-lg font-semibold">
                  {brand}
                </div>
              ))}
            </div>
          </div>
        </Container>
      </section>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-8">
            {services.map((service, index) => (
              <ServiceCard
                key={`${index}-${locale}`}
                title={service.title}
                description={service.description}
                IconComponent={service.IconComponent}
              />
            ))}
          </div>
        </div>
      </section>

      {/* 客户评价区域 */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            className="text-center mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            <h2 className="text-4xl md:text-5xl font-bold font-display gradient-text mb-6">
              {t('home.testimonials.title', localeTyped)}
            </h2>
            <p className="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
              {t('home.testimonials.subtitle', localeTyped)}
            </p>
          </motion.div>
          <div className="grid md:grid-cols-2 gap-8 max-w-5xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard
                key={`${index}-${locale}`}
                name={testimonial.name}
                role={testimonial.role}
                content={testimonial.content}
              />
            ))}
          </div>
        </div>
      </section>

      {/* 快速导航区域 */}
      <section className="py-20 bg-gradient-to-br from-primary-600 to-primary-800 text-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-hero-pattern opacity-5"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <motion.h2
            className="text-4xl md:text-5xl font-bold font-display mb-16"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
          >
            {t('home.quickNav.title', localeTyped)}
          </motion.h2>
          <motion.div
            className="grid sm:grid-cols-3 gap-8"
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            variants={fadeInUp}
            transition={{ duration: 0.5, delay: 0.2 }}
            key={`quicknav-${locale}`}
          >
            <Link href={`/${locale}/about`} className="group">
              <Card glass hover className="text-center p-8 border-white/20 hover:border-white/40 transition-all duration-300">
                <CardContent>
                  <div className="mb-6 flex justify-center">
                    <div className="p-4 bg-white/10 rounded-2xl group-hover:bg-white/20 transition-all duration-300">
                      <NavigationIcons.Building className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  <CardTitle className="text-white mb-4 group-hover:text-secondary-200 transition-colors">
                    {localeTyped === 'zh' ? '关于我们' : 'About Us'}
                  </CardTitle>
                  <CardDescription className="text-primary-100">
                    {localeTyped === 'zh' ? '了解我们的团队和使命' : 'Learn about our team and mission'}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
            <Link href={`/${locale}/services`} className="group">
              <Card glass hover className="text-center p-8 border-white/20 hover:border-white/40 transition-all duration-300">
                <CardContent>
                  <div className="mb-6 flex justify-center">
                    <div className="p-4 bg-white/10 rounded-2xl group-hover:bg-white/20 transition-all duration-300">
                      <NavigationIcons.Briefcase className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  <CardTitle className="text-white mb-4 group-hover:text-secondary-200 transition-colors">
                    {localeTyped === 'zh' ? '服务详情' : 'Our Services'}
                  </CardTitle>
                  <CardDescription className="text-primary-100">
                    {localeTyped === 'zh' ? '查看我们的详细服务内容' : 'View our detailed service offerings'}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
            <Link href={`/${locale}/contact`} className="group">
              <Card glass hover className="text-center p-8 border-white/20 hover:border-white/40 transition-all duration-300">
                <CardContent>
                  <div className="mb-6 flex justify-center">
                    <div className="p-4 bg-white/10 rounded-2xl group-hover:bg-white/20 transition-all duration-300">
                      <ContactIcons.Email className="w-8 h-8 text-white" />
                    </div>
                  </div>
                  <CardTitle className="text-white mb-4 group-hover:text-secondary-200 transition-colors">
                    {localeTyped === 'zh' ? '联系我们' : 'Contact Us'}
                  </CardTitle>
                  <CardDescription className="text-primary-100">
                    {localeTyped === 'zh' ? '开始您的项目咨询' : 'Start your project consultation'}
                  </CardDescription>
                </CardContent>
              </Card>
            </Link>
          </motion.div>
        </div>
      </section>
    </>
  );
}
