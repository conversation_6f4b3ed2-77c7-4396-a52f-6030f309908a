'use client';
import React from 'react';
import Container from '@/components/ui/Container';
import SectionHeader from '@/components/ui/SectionHeader';
import MemberCard from '@/components/ui/MemberCard';
import Stats from '@/components/ui/Stats';
import { useParams } from 'next/navigation';
import type { Locale } from '@/lib/i18n';

export default function Team() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  const members = [
    { name: 'Alice', role: isZh ? '全栈工程师' : 'Full-stack Engineer', avatar: '', skills: ['Next.js', 'Node.js', 'Tailwind'] },
    { name: 'Bob', role: isZh ? '前端工程师' : 'Frontend Engineer', avatar: '', skills: ['React', 'Framer Motion', 'i18n'] },
    { name: '<PERSON>', role: isZh ? '后端工程师' : 'Backend Engineer', avatar: '', skills: ['NestJS', 'PostgreSQL', 'Redis'] },
  ];

  const stats = [
    { label: isZh ? '交付项目' : 'Projects', value: '80+' },
    { label: isZh ? '客户满意度' : 'CSAT', value: '98%' },
    { label: isZh ? '平均交付周期' : 'Avg Cycle', value: '4-8w' },
    { label: isZh ? '覆盖行业' : 'Industries', value: '12+' },
  ];

  return (
    <Container>
      <div className="py-16">
        <SectionHeader title={isZh ? '我们的团队' : 'Our Team'} subtitle={isZh ? '跨领域的实战型工程团队' : 'Practical cross-disciplinary engineering team'} />
        <Stats items={stats} className="mb-12" />
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {members.map((m) => (
            <MemberCard key={m.name} {...m} />
          ))}
        </div>
      </div>
    </Container>
  );
}

