'use client';
import React from 'react';
import Container from '@/components/ui/Container';
import SectionHeader from '@/components/ui/SectionHeader';
import CaseStudyCard from '@/components/ui/CaseStudyCard';
import { useParams } from 'next/navigation';
import type { Locale } from '@/lib/i18n';

export default function CaseStudies() {
  const params = useParams();
  const locale = params.locale as string;
  const localeTyped = locale as Locale;
  const isZh = localeTyped === 'zh';

  const cases = [
    {
      title: isZh ? '跨境电商增长系统' : 'Cross-border E-commerce Growth',
      summary: isZh ? '通过全链路重构与 A/B 实验，实现转化率 28% 提升。' : 'Full-funnel rebuild with A/B experiments improved conversion by 28%.',
      industry: isZh ? '零售 & 电商' : 'Retail & E-commerce',
      metrics: [
        { label: isZh ? '转化率' : 'CVR', value: '+28%' },
        { label: isZh ? '留存' : 'Retention', value: '+18%' },
        { label: isZh ? '渗透率' : 'Penetration', value: '+12%' },
        { label: isZh ? '故障率' : 'Incidents', value: '-42%' },
      ],
      tags: ['Next.js', 'Node.js', 'Experiment']
    },
  ];

  return (
    <Container>
      <div className="py-16">
        <SectionHeader title={isZh ? '客户案例' : 'Case Studies'} subtitle={isZh ? '以结果为导向的实践沉淀' : 'Outcome-driven engineering practices'} />
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {cases.map((c) => (
            <CaseStudyCard key={c.title} {...c} />
          ))}
        </div>
      </div>
    </Container>
  );
}

