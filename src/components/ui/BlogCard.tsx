'use client';
import React from 'react';
import { motion } from 'framer-motion';
import Card, { CardContent, CardDescription, CardHeader, CardTitle } from './Card';
import Badge from './Badge';

// 博客卡片，用于 /blog 列表
// Confirmed via mcp-feedback-enhanced
export interface BlogCardProps {
  title: string;
  excerpt: string;
  date: string;
  tags?: string[];
  onClick?: () => void;
}

export default function BlogCard({ title, excerpt, date, tags = [], onClick }: BlogCardProps) {
  return (
    <Card onClick={onClick} className="group" padding="lg" shadow="lg" rounded="xl" hover>
      <CardHeader>
        <CardTitle size="lg">{title}</CardTitle>
        <CardDescription>{excerpt}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="flex items-center justify-between text-sm text-neutral-500">
          <span>{date}</span>
          <div className="flex gap-2">
            {tags.slice(0, 3).map((t) => (
              <Badge key={t} color="neutral" soft>{t}</Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

