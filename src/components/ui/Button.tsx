'use client';

import React from 'react';
import { motion, type HTMLMotionProps } from 'framer-motion';
import { type LucideIcon } from 'lucide-react';

// 规避 React HTMLButtonElement 的 onDrag 事件类型与 framer-motion 的 onDrag 冲突
// 使用 Omit 去除 onDrag，避免类型不兼容导致构建失败
// Confirmed via mcp-feedback-enhanced
// 将原生 button props 与 framer-motion 的动画 props 进行兼容
// 移除与 motion 冲突的拖拽事件定义
export type MotionButtonProps = Omit<HTMLMotionProps<'button'>, 'onDrag' | 'onDragStart' | 'onDragEnd' | 'onDragTransitionEnd' | 'onDragMomentumEnd'>;

interface ButtonProps extends MotionButtonProps {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  icon?: LucideIcon;
  iconPosition?: 'left' | 'right';
  loading?: boolean;
  fullWidth?: boolean;
  gradient?: boolean;
  children: React.ReactNode;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({
    className = '',
    variant = 'primary',
    size = 'md',
    icon: Icon,
    iconPosition = 'left',
    loading = false,
    fullWidth = false,
    gradient = false,
    children,
    disabled,
    ...props
  }, ref) => {
    // 基础类：更柔和的过渡、可视化焦点、圆角增强
    // 修改 - 基础按钮更克制：弱化阴影/缩放动画，明确焦点环，圆角趋近 iOS 默认 [Apple HIG]
const baseClasses = 'inline-flex items-center justify-center font-medium transition-colors duration-200 focus:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg';

    const variantClasses = {
      primary: gradient
        ? 'bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500'
        : 'bg-primary-600 text-white hover:bg-primary-700 focus-visible:ring-primary-500',
      secondary: gradient
        ? 'bg-secondary-600 text-white hover:bg-secondary-700 focus-visible:ring-secondary-500'
        : 'bg-secondary-600 text-white hover:bg-secondary-700 focus-visible:ring-secondary-500',
      outline: 'border border-neutral-300 text-neutral-900 hover:bg-neutral-100 focus-visible:ring-neutral-400',
      ghost: 'text-neutral-900 hover:bg-neutral-100 focus-visible:ring-neutral-400',
      destructive: 'bg-red-600 text-white hover:bg-red-700 focus-visible:ring-red-500',
    };

    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm rounded-md gap-1.5',
      md: 'px-4 py-2 text-base rounded-lg gap-2',
      lg: 'px-6 py-3 text-lg rounded-lg gap-2.5',
      xl: 'px-8 py-4 text-xl rounded-xl gap-3',
    };

    const iconSizes = {
      sm: 16,
      md: 18,
      lg: 20,
      xl: 24,
    };

    const classes = `
      ${baseClasses}
      ${variantClasses[variant]}
      ${sizeClasses[size]}
      ${fullWidth ? 'w-full' : ''}
      ${className}
    `.trim();

    return (
      <motion.button
        ref={ref}
        className={classes}
        disabled={disabled || loading}
        // 修改 - 更克制的微交互：减少缩放幅度，避免眩晕 [Apple HIG]
        whileHover={{ scale: 1.005 }}
        whileTap={{ scale: 0.995 }}
        {...props}
      >
        {loading && (
          <svg
            className="animate-spin -ml-1 mr-2 h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        )}

        {Icon && iconPosition === 'left' && !loading && (
          <Icon size={iconSizes[size]} />
        )}

        <span>{children}</span>

        {Icon && iconPosition === 'right' && !loading && (
          <Icon size={iconSizes[size]} />
        )}
      </motion.button>
    );
  }
);

Button.displayName = 'Button';

export default Button;
