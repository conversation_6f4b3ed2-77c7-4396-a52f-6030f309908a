import React from 'react';

// 区块标题：主标题 + 副标题 + 轻分割线
// Confirmed via mcp-feedback-enhanced
interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  align?: 'left' | 'center';
  className?: string;
}

export default function SectionHeader({ title, subtitle, align = 'center', className = '' }: SectionHeaderProps) {
  return (
    <div className={`mb-10 ${align === 'center' ? 'text-center' : ''} ${className}`.trim()}>
      <h2 className="text-2xl md:text-3xl font-bold font-display tracking-tight gradient-text">
        {title}
      </h2>
      {subtitle && (
        <p className="mt-3 text-neutral-600 max-w-2xl mx-auto">{subtitle}</p>
      )}
      <div className="mt-6 h-px w-24 mx-auto bg-gradient-to-r from-primary-400 via-accent-400 to-secondary-400 opacity-60" />
    </div>
  );
}

