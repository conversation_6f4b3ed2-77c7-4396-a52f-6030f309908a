'use client';
import React from 'react';
import Card, { CardContent, CardDescription, CardHeader, CardTitle } from './Card';
import Badge from './Badge';

// 客户案例卡片
// Confirmed via mcp-feedback-enhanced
export interface CaseStudyCardProps {
  title: string;
  summary: string;
  industry?: string;
  metrics?: Array<{ label: string; value: string }>;
  tags?: string[];
}

export default function CaseStudyCard({ title, summary, industry, metrics = [], tags = [] }: CaseStudyCardProps) {
  return (
    <Card padding="lg" shadow="lg" rounded="xl" hover>
      <CardHeader>
        <CardTitle size="lg">{title}</CardTitle>
        <CardDescription>{summary}</CardDescription>
      </CardHeader>
      <CardContent>
        {industry && (
          <div className="mb-3"><Badge color="accent" soft>{industry}</Badge></div>
        )}
        {metrics.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 mb-4 text-sm">
            {metrics.map((m) => (
              <div key={m.label} className="p-3 bg-neutral-50 rounded-lg text-center">
                <div className="font-semibold text-neutral-900">{m.value}</div>
                <div className="text-neutral-500">{m.label}</div>
              </div>
            ))}
          </div>
        )}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-2">
            {tags.map((t) => (
              <Badge key={t} color="neutral" soft>{t}</Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

