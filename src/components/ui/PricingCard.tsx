'use client';
import React from 'react';
import Card, { Card<PERSON>ontent, CardHeader, CardTitle } from './Card';
import Button from './Button';

// 价格卡片（整卡可点击）
// Confirmed via mcp-feedback-enhanced
export interface PricingCardProps {
  name: string;
  price: string;
  features: string[];
  highlight?: boolean;
  onSelect?: () => void;
}

export default function PricingCard({ name, price, features, highlight = false, onSelect }: PricingCardProps) {
  return (
    // 整卡可点击，提升可达性与可用性
    <Card padding="lg" shadow={highlight ? 'xl' : 'lg'} rounded="xl" hover onClick={onSelect} className={highlight ? 'border-2 border-primary-200 cursor-pointer' : 'cursor-pointer'}>
      <CardHeader>
        <div className="mb-2 text-sm uppercase tracking-wide text-neutral-500">{name}</div>
        <CardTitle size="xl"><span className="gradient-text">{price}</span></CardTitle>
      </CardHeader>
      <CardContent>
        <ul className="space-y-2 mb-6 text-sm">
          {features.map((f) => (
            <li key={f} className="flex items-start gap-2">
              <span className="mt-1 inline-block w-1.5 h-1.5 rounded-full bg-primary-500" />
              <span>{f}</span>
            </li>
          ))}
        </ul>
        <Button variant={highlight ? 'secondary' : 'primary'} fullWidth onClick={onSelect}>选择方案</Button>
      </CardContent>
    </Card>
  );
}

