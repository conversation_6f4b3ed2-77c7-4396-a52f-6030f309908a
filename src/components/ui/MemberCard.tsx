'use client';
import React from 'react';
import Card, { CardContent, CardDescription, CardHeader, CardTitle } from './Card';
import Badge from './Badge';

// 团队成员卡片
// Confirmed via mcp-feedback-enhanced
export interface MemberCardProps {
  name: string;
  role: string;
  avatar?: string; // img url
  skills?: string[];
  bio?: string; // 简介，可选
}

export default function MemberCard({ name, role, avatar, skills = [], bio }: MemberCardProps) {
  return (
    <Card padding="lg" shadow="lg" rounded="xl" hover className="text-center">
      <CardHeader>
        <div className="mx-auto w-24 h-24 rounded-full bg-neutral-100 overflow-hidden mb-4 hover-zoom">
          {avatar ? (
            // eslint-disable-next-line @next/next/no-img-element
            <img src={avatar} alt={name} className="w-full h-full object-cover" />
          ) : null}
        </div>
        <CardTitle size="lg" gradient>{name}</CardTitle>
        <CardDescription>{role}</CardDescription>
      </CardHeader>
      <CardContent>
        {bio && <p className="text-neutral-600 mb-4 text-sm">{bio}</p>}
        <div className="flex flex-wrap gap-2 justify-center">
          {skills.map((s) => (
            <Badge key={s} color="primary" soft>
              {s}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

