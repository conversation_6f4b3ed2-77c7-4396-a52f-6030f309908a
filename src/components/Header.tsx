'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useRouter, usePathname } from 'next/navigation';
import Link from 'next/link';
import { t, type Locale } from '@/lib/i18n';
import { NavigationIcons } from '@/components/ui/Icons';
import Button from '@/components/ui/Button';

interface HeaderProps {
  locale: Locale;
}

const Header = ({ locale }: HeaderProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [langOpen, setLangOpen] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  // 确保locale有效，如果无效则使用默认值
  const validLocale = locale || 'zh';

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const toggleLangMenu = () => {
    setLangOpen(!langOpen);
  };

  const menuItems = [
    { name: t('navigation.home', validLocale), href: `/${validLocale}` },
    { name: t('navigation.about', validLocale), href: `/${validLocale}/about` },
    { name: t('navigation.services', validLocale), href: `/${validLocale}/services` },
    { name: t('navigation.portfolio', validLocale), href: `/${validLocale}/portfolio` },
    { name: t('navigation.blog', validLocale), href: `/${validLocale}/blog` },
    { name: t('navigation.team', validLocale), href: `/${validLocale}/team` },
    { name: t('navigation.caseStudies', validLocale), href: `/${validLocale}/case-studies` },
    { name: t('navigation.pricing', validLocale), href: `/${validLocale}/pricing` },
    { name: t('navigation.contact', validLocale), href: `/${validLocale}/contact` },
  ];

  const languages = [
    { code: 'zh', name: '中文', flag: '🇨🇳' },
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'fr', name: 'Français', flag: '🇫🇷' },
    { code: 'de', name: 'Deutsch', flag: '🇩🇪' },
    { code: 'ja', name: '日本語', flag: '🇯🇵' },
    { code: 'ko', name: '한국어', flag: '🇰🇷' },
    { code: 'es', name: 'Español', flag: '🇪🇸' },
    { code: 'pt', name: 'Português', flag: '🇵🇹' },
    { code: 'ru', name: 'Русский', flag: '🇷🇺' },
    { code: 'it', name: 'Italiano', flag: '🇮🇹' },
  ];

  const currentLang = languages.find(lang => lang.code === validLocale);

  const handleLanguageChange = (newLocale: string) => {
    const newPath = pathname.replace(`/${validLocale}`, `/${newLocale}`);
    router.push(newPath);
    setLangOpen(false);
  };

  return (
      <header className="bg-white/80 supports-[backdrop-filter]:bg-white/70 backdrop-blur-md sticky top-0 z-50 border-b border-neutral-200/60">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-18">
          <div className="flex items-center">
            <Link href={`/${validLocale}`}>
              <span className="flex items-center text-xl md:text-2xl font-semibold tracking-[-0.01em] text-neutral-900">
                {validLocale === 'zh' ? '三娃软件开发工作室' : 'Sanva Studio'}
              </span>
            </Link>
          </div>

          {/* 桌面导航 */}
          <nav className="hidden md:ml-6 md:flex md:space-x-2 md:items-center">
            {menuItems.map((item) => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`relative px-3 py-1.5 rounded-md text-sm font-medium transition-colors duration-200 ${
                    isActive
                      ? 'text-neutral-900 bg-neutral-100'
                      : 'text-neutral-700 hover:text-neutral-900 hover:bg-neutral-100'
                  }`}
                >
                  {item.name}
                  {isActive && (
                    <motion.div
                      // 修改 - 活动下划线采用更细腻的系统蓝，降低视觉冲击 [Apple HIG]
className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary-600/80 rounded-full"
                      layoutId="activeTab"
                    />
                  )}
                </Link>
              );
            })}
            
            {/* 语言切换器 */}
            <div className="relative ml-4">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleLangMenu}
                icon={langOpen ? NavigationIcons.ChevronUp : NavigationIcons.ChevronDown}
                iconPosition="right"
                // 修改 - 语言按钮改为中性边框与文字，hover 轻度变化 [Apple HIG]
className="border-neutral-300 text-neutral-700 hover:border-neutral-400 hover:text-neutral-900"
              >
                <span className="mr-2">{currentLang?.flag}</span>
                <span className="hidden sm:inline">{currentLang?.name}</span>
              </Button>

              {langOpen && (
                <motion.div
                  // 修改 - 下拉菜单采用更浅的投影与圆角 [Apple HIG]
className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-soft ring-1 ring-neutral-200 focus:outline-none z-50 overflow-hidden"
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="py-2">
                    {languages.map((lang) => (
                      <button
                        key={lang.code}
                        onClick={() => handleLanguageChange(lang.code)}
                        className={`${
                          validLocale === lang.code
                            ? 'bg-neutral-100 text-neutral-900 border-r-2 border-primary-600/60'
                            : 'text-neutral-700 hover:bg-neutral-50'
                        } flex items-center w-full px-4 py-3 text-sm transition-colors duration-200`}
                      >
                        <span className="mr-3 text-lg">{lang.flag}</span>
                        <span className="font-medium">{lang.name}</span>
                        {validLocale === lang.code && (
                          <NavigationIcons.CheckCircle className="ml-auto w-4 h-4 text-primary-600" />
                        )}
                      </button>
                    ))}
                  </div>
                </motion.div>
              )}
            </div>
          </nav>

          {/* 移动端菜单按钮 */}
          <div className="flex items-center md:hidden">
            <button
              onClick={toggleMenu}
              className="inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-primary focus:outline-none"
              aria-expanded={isOpen}
              aria-controls="mobile-menu"
              aria-label={isOpen ? '关闭主菜单' : '打开主菜单'}
            >
              <span className="sr-only">{isOpen ? '关闭主菜单' : '打开主菜单'}</span>
              <svg
                className={`${isOpen ? 'hidden' : 'block'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                role="img"
                aria-hidden={isOpen}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
              <svg
                className={`${isOpen ? 'block' : 'hidden'} h-6 w-6`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                role="img"
                aria-hidden={!isOpen}
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* 移动端菜单 */}
      <motion.div
        id="mobile-menu"
        className={`${isOpen ? 'block' : 'hidden'} md:hidden`}
        initial={{ opacity: 0, height: 0 }}
        animate={{ opacity: isOpen ? 1 : 0, height: isOpen ? 'auto' : 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="pt-2 pb-3 space-y-1">
          {menuItems.map((item) => (
            <Link
              key={item.name}
              href={item.href}
              className="block pl-3 pr-4 py-2 border-l-4 border-transparent text-base font-medium text-gray-600 hover:bg-gray-50 hover:border-secondary hover:text-gray-900 transition-colors duration-200"
              onClick={() => setIsOpen(false)}
            >
              {item.name}
            </Link>
          ))}
          
          {/* 移动端语言切换 */}
          <div className="border-t pt-3 mt-3">
            <div className="px-3 pb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider">
              语言 / Language
            </div>
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code)}
                className={`${
                  validLocale === lang.code ? 'bg-gray-100 text-gray-900' : 'text-gray-600'
                } flex items-center w-full pl-3 pr-4 py-2 text-base font-medium hover:bg-gray-50 hover:text-gray-900`}
              >
                <span className="mr-3">{lang.flag}</span>
                <span>{lang.name}</span>
              </button>
            ))}
          </div>
        </div>
      </motion.div>
    </header>
  );
};

export default Header; 