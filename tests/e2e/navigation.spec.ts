import { test, expect } from '@playwright/test';

// 导航测试：检测顶部导航链接是否正常工作
const locales = ['zh', 'en'] as const;

for (const locale of locales) {
  test.describe(`Navigation tests - ${locale}`, () => {
    test.beforeEach(async ({ page }) => {
      // 访问首页
      await page.goto(`/${locale}`);
      await expect(page.locator('header')).toBeVisible();
    });

    const navigationItems = [
      { name: 'home', path: `/${locale}` },
      { name: 'about', path: `/${locale}/about` },
      { name: 'services', path: `/${locale}/services` },
      { name: 'portfolio', path: `/${locale}/portfolio` },
      { name: 'blog', path: `/${locale}/blog` },
      { name: 'team', path: `/${locale}/team` },
      { name: 'case-studies', path: `/${locale}/case-studies` },
      { name: 'pricing', path: `/${locale}/pricing` },
      { name: 'contact', path: `/${locale}/contact` },
    ];

    for (const item of navigationItems) {
      test(`Navigation to ${item.name} page works`, async ({ page }) => {
        // 等待页面完全加载
        await page.waitForLoadState('domcontentloaded');
        await expect(page.locator('header')).toBeVisible();

        // 查找导航链接
        const navLink = page.locator(`header nav a[href="${item.path}"]`);

        // 检查链接是否存在并可见
        await expect(navLink).toBeVisible();

        // 点击链接前记录当前URL
        const currentUrl = page.url();
        console.log(`Current URL before click: ${currentUrl}`);
        console.log(`Clicking link to: ${item.path}`);

        // 点击链接
        await navLink.click();

        // 等待导航完成
        await page.waitForURL(`**${item.path}`, { timeout: 10000 });

        // 等待页面加载完成
        await page.waitForLoadState('networkidle');

        // 验证URL是否正确
        const newUrl = page.url();
        console.log(`New URL after click: ${newUrl}`);
        expect(newUrl).toContain(item.path);

        // 验证页面内容是否加载
        await expect(page.locator('main')).toBeVisible();

        // 检查是否有错误
        const errorMessages = await page.locator('text=/error|Error|错误|404|500/i').count();
        expect(errorMessages).toBe(0);

        // 验证页面标题存在
        const title = await page.title();
        expect(title).toBeTruthy();
        expect(title.length).toBeGreaterThan(0);
      });
    }

    test('Mobile navigation works', async ({ page }) => {
      // 设置移动端视口
      await page.setViewportSize({ width: 375, height: 667 });

      // 等待页面加载
      await page.waitForLoadState('domcontentloaded');
      await expect(page.locator('header')).toBeVisible();

      // 查找移动端菜单按钮
      const menuButton = page.locator('header button[aria-expanded]');
      await expect(menuButton).toBeVisible();

      // 点击菜单按钮
      await menuButton.click();

      // 验证移动端菜单是否显示
      const mobileMenu = page.locator('#mobile-menu');
      await expect(mobileMenu).toBeVisible();

      // 测试移动端导航链接 - 使用完整路径
      const aboutPath = `/${locale}/about`;
      const mobileNavLink = page.locator(`#mobile-menu a[href="${aboutPath}"]`).first();
      await expect(mobileNavLink).toBeVisible();

      console.log(`Clicking mobile nav link to: ${aboutPath}`);
      await mobileNavLink.click();

      // 等待导航完成
      await page.waitForURL(`**${aboutPath}`, { timeout: 10000 });
      await page.waitForLoadState('networkidle');

      const newUrl = page.url();
      console.log(`Mobile nav new URL: ${newUrl}`);
      expect(newUrl).toContain(aboutPath);
      await expect(page.locator('main')).toBeVisible();
    });

    test('Language switching works', async ({ page }) => {
      // 等待页面加载
      await page.waitForLoadState('domcontentloaded');
      await expect(page.locator('header')).toBeVisible();

      // 查找语言切换按钮 - 使用更精确的选择器，只选择桌面版的按钮
      const langButton = page.locator('header nav button').filter({ hasText: locale === 'zh' ? '🇨🇳' : '🇺🇸' }).first();
      await expect(langButton).toBeVisible();

      console.log(`Current locale: ${locale}, clicking language button`);

      // 点击语言按钮
      await langButton.click();

      // 验证语言菜单是否显示
      const langMenu = page.locator('div').filter({ hasText: locale === 'zh' ? 'English' : '中文' }).first();
      await expect(langMenu).toBeVisible();

      // 选择另一种语言
      const targetLocale = locale === 'zh' ? 'en' : 'zh';
      const targetLangText = targetLocale === 'zh' ? '中文' : 'English';
      const targetLangButton = page.locator(`button`).filter({ hasText: targetLangText }).first();

      if (await targetLangButton.count() > 0) {
        console.log(`Switching to locale: ${targetLocale}`);
        await targetLangButton.click();

        // 等待导航完成
        await page.waitForURL(`**/${targetLocale}**`, { timeout: 10000 });
        await page.waitForLoadState('networkidle');

        // 验证URL是否包含新的语言代码
        const newUrl = page.url();
        console.log(`Language switch new URL: ${newUrl}`);
        expect(newUrl).toContain(`/${targetLocale}`);
      }
    });
  });
}
